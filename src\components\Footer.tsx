"use client";

import React from "react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Phone,
  Mail,
  MapPin,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
} from "lucide-react";

const Footer: React.FC = () => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  const navigation = [
    { name: t("nav.home"), href: "/" },
    { name: t("nav.about"), href: "/about" },
    { name: t("nav.services"), href: "/services" },
    { name: t("nav.portfolio"), href: "/portfolio" },
    { name: t("nav.blog"), href: "/blog" },
    { name: t("nav.contact"), href: "/contact" },
  ];

  const services = [
    { name: t("services.brokerage.title"), href: "/services#brokerage" },
    { name: t("services.assets.title"), href: "/services#assets" },
    { name: t("services.investments.title"), href: "/services#investments" },
    { name: t("services.media.title"), href: "/services#media" },
  ];

  const socialLinks = [
    { name: "Facebook", icon: Facebook, href: "#" },
    { name: "Twitter", icon: Twitter, href: "#" },
    { name: "LinkedIn", icon: Linkedin, href: "#" },
    { name: "Instagram", icon: Instagram, href: "#" },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">NV</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold">NewVision</span>
                <span className="text-xs text-gray-400">
                  {language === "en" ? "Business Solutions" : "الحلول التجارية"}
                </span>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              {language === "en"
                ? "Excellence in commercial brokerage, asset management, investments, and media production. Building success through expertise and innovation."
                : "التميز في الوساطة التجارية وإدارة الأصول والاستثمارات وإنتاج الوسائط. بناء النجاح من خلال الخبرة والابتكار."}
            </p>
            <div className="flex space-x-4 rtl:space-x-reverse">
              {socialLinks.map(({ name, icon: Icon, href }) => (
                <a
                  key={name}
                  href={href}
                  className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-yellow-600 transition-colors"
                  aria-label={name}
                >
                  <Icon className="w-4 h-4" />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-400">
              {language === "en" ? "Quick Links" : "روابط سريعة"}
            </h3>
            <ul className="space-y-2">
              {navigation.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-300 hover:text-yellow-400 transition-colors text-sm"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-400">
              {t("services.title")}
            </h3>
            <ul className="space-y-2">
              {services.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-300 hover:text-yellow-400 transition-colors text-sm"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-400">
              {t("contact.title")}
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Phone className="w-4 h-4 text-yellow-400" />
                <span className="text-gray-300 text-sm">+20 1X XXXX XXXX</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Mail className="w-4 h-4 text-yellow-400" />
                <span className="text-gray-300 text-sm">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <MapPin className="w-4 h-4 text-yellow-400 mt-0.5" />
                <span className="text-gray-300 text-sm">
                  {language === "en"
                    ? "Mit Ghamr, Dakahlia, Egypt"
                    : "ميت غمر، الدقهلية، مصر"}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p className="text-gray-400 text-sm">
            © 2024 NewVision.{" "}
            {language === "en" ? "All rights reserved." : "جميع الحقوق محفوظة."}
          </p>
          <div className="flex space-x-6 rtl:space-x-reverse">
            <Link
              href="/privacy"
              className="text-gray-400 hover:text-yellow-400 transition-colors text-sm"
            >
              {language === "en" ? "Privacy Policy" : "سياسة الخصوصية"}
            </Link>
            <Link
              href="/terms"
              className="text-gray-400 hover:text-yellow-400 transition-colors text-sm"
            >
              {language === "en" ? "Terms of Service" : "شروط الخدمة"}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
