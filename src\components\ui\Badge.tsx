"use client";

import React from "react";
import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";

interface BadgeProps {
  children: React.ReactNode;
  variant?: "default" | "primary" | "secondary" | "success" | "warning" | "error" | "glass";
  size?: "sm" | "md" | "lg";
  icon?: LucideIcon;
  pulse?: boolean;
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = "default",
  size = "md",
  icon: Icon,
  pulse = false,
  className = "",
}) => {
  const variants = {
    default: "bg-gray-100 text-gray-800 border-gray-200",
    primary: "bg-primary-100 text-primary-800 border-primary-200",
    secondary: "bg-secondary-100 text-secondary-800 border-secondary-200",
    success: "bg-green-100 text-green-800 border-green-200",
    warning: "bg-yellow-100 text-yellow-800 border-yellow-200",
    error: "bg-red-100 text-red-800 border-red-200",
    glass: "bg-white/10 text-white border-white/20 backdrop-blur-sm",
  };

  const sizes = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1.5 text-sm",
    lg: "px-4 py-2 text-base",
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  };

  const baseClasses = "inline-flex items-center font-medium rounded-full border transition-all duration-300";

  return (
    <motion.span
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className} ${
        pulse ? "animate-pulse-soft" : ""
      }`}
    >
      {Icon && (
        <Icon className={`${iconSizes[size]} mr-1.5 rtl:mr-0 rtl:ml-1.5`} />
      )}
      {children}
    </motion.span>
  );
};

export default Badge;
