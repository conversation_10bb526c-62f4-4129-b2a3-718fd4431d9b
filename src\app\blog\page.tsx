"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  User,
  ArrowRight,
  Search,
  Tag,
  TrendingUp,
  Building2,
  Shield,
  Video,
} from "lucide-react";

const BlogPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", label: isRTL ? "جميع المقالات" : "All Articles", icon: null },
    {
      id: "brokerage",
      label: isRTL ? "الوساطة التجارية" : "Commercial Brokerage",
      icon: Building2,
    },
    {
      id: "investments",
      label: isRTL ? "الاستثمارات" : "Investments",
      icon: TrendingUp,
    },
    {
      id: "assets",
      label: isRTL ? "إدارة الأصول" : "Asset Management",
      icon: Shield,
    },
    {
      id: "media",
      label: isRTL ? "الإعلام والتسويق" : "Media & Marketing",
      icon: Video,
    },
  ];

  const articles = [
    {
      id: 1,
      title: isRTL
        ? "مستقبل التجارة الإلكترونية في جمهورية مصر العربية"
        : "The Future of E-commerce in Arab Republic of Egypt",
      excerpt: isRTL
        ? "نظرة شاملة على الفرص الاستثمارية في قطاع التجارة الإلكترونية وتأثير رؤية مصر 2030"
        : "A comprehensive look at investment opportunities in e-commerce sector and Egypt Vision 2030 impact",
      category: "investments",
      author: isRTL ? "أحمد محمد" : "Ahmed Mohammed",
      date: "2024-01-15",
      readTime: isRTL ? "5 دقائق" : "5 min read",
      image:
        "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      featured: true,
    },
    {
      id: 2,
      title: isRTL
        ? "استراتيجيات إدارة المحافظ الاستثمارية في 2024"
        : "Investment Portfolio Management Strategies for 2024",
      excerpt: isRTL
        ? "أحدث الاستراتيجيات لتنويع المحافظ الاستثمارية وإدارة المخاطر في العام الجديد"
        : "Latest strategies for portfolio diversification and risk management in the new year",
      category: "investments",
      author: isRTL ? "فاطمة السعيد" : "Fatima Al-Saeed",
      date: "2024-01-10",
      readTime: isRTL ? "7 دقائق" : "7 min read",
      image:
        "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      featured: false,
    },
    {
      id: 3,
      title: isRTL
        ? "دليل المبتدئين للاستثمار في العقارات المصرية"
        : "Beginner's Guide to Real Estate Investment in Egypt",
      excerpt: isRTL
        ? "خطوات عملية للبدء في الاستثمار العقاري وأهم النصائح لتحقيق عوائد مجزية"
        : "Practical steps to start real estate investment and key tips for achieving profitable returns",
      category: "assets",
      author: isRTL ? "خالد الشمري" : "Khalid Al-Shamri",
      date: "2024-01-05",
      readTime: isRTL ? "6 دقائق" : "6 min read",
      image:
        "https://images.unsplash.com/photo-1486406146494-da3fb1916ddf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      featured: false,
    },
    {
      id: 4,
      title: isRTL
        ? "التسويق الرقمي: ثورة في عالم الأعمال"
        : "Digital Marketing: A Business Revolution",
      excerpt: isRTL
        ? "كيف غيّر التسويق الرقمي طريقة تفاعل الشركات مع عملائها وتحقيق النمو"
        : "How digital marketing has transformed the way businesses interact with customers and achieve growth",
      category: "media",
      author: isRTL ? "سارة النور" : "Sarah Al-Noor",
      date: "2023-12-28",
      readTime: isRTL ? "4 دقائق" : "4 min read",
      image:
        "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      featured: false,
    },
    {
      id: 5,
      title: isRTL
        ? "أساسيات الوساطة التجارية في الأسواق الناشئة"
        : "Commercial Brokerage Fundamentals in Emerging Markets",
      excerpt: isRTL
        ? "فهم عميق لآليات الوساطة التجارية وأهميتها في تطوير الأسواق الناشئة"
        : "Deep understanding of commercial brokerage mechanisms and their importance in developing emerging markets",
      category: "brokerage",
      author: isRTL ? "محمد العتيبي" : "Mohammed Al-Otaibi",
      date: "2023-12-20",
      readTime: isRTL ? "8 دقائق" : "8 min read",
      image:
        "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      featured: false,
    },
    {
      id: 6,
      title: isRTL
        ? "تقنيات إدارة المخاطر في الاستثمارات"
        : "Risk Management Techniques in Investments",
      excerpt: isRTL
        ? "أدوات وتقنيات حديثة لتقييم وإدارة المخاطر في الاستثمارات المختلفة"
        : "Modern tools and techniques for assessing and managing risks in various investments",
      category: "investments",
      author: isRTL ? "عبدالله الحربي" : "Abdullah Al-Harbi",
      date: "2023-12-15",
      readTime: isRTL ? "6 دقائق" : "6 min read",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      featured: false,
    },
  ];

  const filteredArticles = articles.filter((article) => {
    const matchesCategory =
      selectedCategory === "all" || article.category === selectedCategory;
    const matchesSearch =
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredArticle = articles.find((article) => article.featured);
  const regularArticles = filteredArticles.filter(
    (article) => !article.featured
  );

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80")',
          }}
        ></div>

        <div className="relative container mx-auto px-4 text-center text-white">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-6xl font-bold mb-6"
          >
            {isRTL ? "مدونة نيو فيجن" : "NewVision Blog"}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto"
          >
            {isRTL
              ? "آخر المقالات والرؤى في عالم الأعمال والاستثمارات"
              : "Latest articles and insights in business and investment world"}
          </motion.p>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-12 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative w-full lg:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={
                  isRTL ? "ابحث في المقالات..." : "Search articles..."
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-lg font-medium transition-all ${
                    selectedCategory === category.id
                      ? "bg-yellow-500 text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  {category.icon && <category.icon className="w-4 h-4" />}
                  <span>{category.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Article */}
      {featuredArticle && selectedCategory === "all" && !searchTerm && (
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl shadow-xl overflow-hidden"
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                <div className="aspect-video lg:aspect-auto">
                  <img
                    src={featuredArticle.image}
                    alt={featuredArticle.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
                    <span className="bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full">
                      {isRTL ? "مقال مميز" : "Featured"}
                    </span>
                    <span className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                      {
                        categories.find(
                          (c) => c.id === featuredArticle.category
                        )?.label
                      }
                    </span>
                  </div>

                  <h2 className="text-3xl font-bold text-gray-900 mb-4">
                    {featuredArticle.title}
                  </h2>

                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {featuredArticle.excerpt}
                  </p>

                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <User className="w-4 h-4" />
                        <span>{featuredArticle.author}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Calendar className="w-4 h-4" />
                        <span>{featuredArticle.date}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Clock className="w-4 h-4" />
                        <span>{featuredArticle.readTime}</span>
                      </div>
                    </div>
                  </div>

                  <button className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black font-semibold py-3 px-6 rounded-lg hover:from-yellow-500 hover:to-yellow-700 transition-all transform hover:scale-105 flex items-center space-x-2 rtl:space-x-reverse w-fit">
                    <span>{isRTL ? "قراءة المقال" : "Read Article"}</span>
                    <ArrowRight
                      className={`w-4 h-4 ${isRTL ? "rtl-flip" : ""}`}
                    />
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Articles Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {isRTL ? "أحدث المقالات" : "Latest Articles"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {isRTL
                ? "تابع آخر التطورات والتحليلات في عالم الأعمال والاستثمارات"
                : "Follow the latest developments and analyses in the world of business and investments"}
            </p>
          </motion.div>

          {regularArticles.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {regularArticles.map((article, index) => (
                <motion.article
                  key={article.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
                >
                  <div className="aspect-video overflow-hidden">
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="bg-gray-100 text-gray-800 text-xs font-medium px-3 py-1 rounded-full">
                        {
                          categories.find((c) => c.id === article.category)
                            ?.label
                        }
                      </span>
                      <div className="flex items-center text-gray-500 text-sm">
                        <Clock className="w-4 h-4 mr-1" />
                        {article.readTime}
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-yellow-600 transition-colors line-clamp-2">
                      {article.title}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {article.excerpt}
                    </p>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <User className="w-4 h-4" />
                        <span>{article.author}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Calendar className="w-4 h-4" />
                        <span>{article.date}</span>
                      </div>
                    </div>

                    <button className="w-full bg-gray-100 hover:bg-yellow-500 hover:text-white text-gray-700 font-medium py-2 px-4 rounded-lg transition-all flex items-center justify-center space-x-2 rtl:space-x-reverse">
                      <span>{isRTL ? "قراءة المزيد" : "Read More"}</span>
                      <ArrowRight
                        className={`w-4 h-4 ${isRTL ? "rtl-flip" : ""}`}
                      />
                    </button>
                  </div>
                </motion.article>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {isRTL ? "لا توجد مقالات" : "No Articles Found"}
              </h3>
              <p className="text-gray-500">
                {isRTL
                  ? "جرب تغيير مصطلحات البحث أو الفئة المحددة"
                  : "Try changing your search terms or selected category"}
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Subscription */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-blue-900">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <h3 className="text-3xl font-bold text-white mb-4">
              {isRTL
                ? "اشترك في نشرتنا الإخبارية"
                : "Subscribe to Our Newsletter"}
            </h3>
            <p className="text-gray-200 mb-8">
              {isRTL
                ? "احصل على آخر المقالات والرؤى التجارية مباشرة في بريدك الإلكتروني"
                : "Get the latest articles and business insights delivered directly to your inbox"}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder={
                  isRTL ? "أدخل بريدك الإلكتروني" : "Enter your email"
                }
                className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-yellow-500"
              />
              <button className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black font-semibold px-6 py-3 rounded-lg hover:from-yellow-500 hover:to-yellow-700 transition-all">
                {isRTL ? "اشترك" : "Subscribe"}
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
