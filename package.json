{"name": "newvision-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next/font": "^14.2.15", "framer-motion": "^12.23.12", "geist": "^1.4.2", "lucide-react": "^0.534.0", "next": "15.4.5", "next-i18next": "^15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-i18next": "^15.6.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.4.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}