"use client";

import React from "react";
import { motion } from "framer-motion";

interface CardProps {
  children: React.ReactNode;
  variant?: "default" | "glass" | "gradient" | "hover" | "bordered";
  className?: string;
  hover?: boolean;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = "default",
  className = "",
  hover = false,
  onClick,
}) => {
  const baseClasses = "rounded-2xl transition-all duration-300 ease-out";

  const variants = {
    default: "bg-white shadow-soft border border-gray-100",
    glass: "glass-card",
    gradient: "bg-gradient-to-br from-white to-gray-50 shadow-medium border border-gray-200",
    hover: "bg-white shadow-soft border border-gray-100 card-hover",
    bordered: "bg-white border-2 border-primary-200 shadow-soft",
  };

  const hoverClasses = hover ? "card-hover cursor-pointer" : "";

  const classes = `${baseClasses} ${variants[variant]} ${hoverClasses} ${className}`;

  if (onClick) {
    return (
      <motion.div
        className={classes}
        onClick={onClick}
        whileHover={{ scale: 1.02, y: -4 }}
        whileTap={{ scale: 0.98 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={classes}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
};

export default Card;
