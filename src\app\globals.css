@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  --gold: #d4af37;
  --gold-light: #f4e5a3;
  --gold-dark: #b8941f;
  --navy: #1e3a8a;
  --navy-light: #3b82f6;
  --navy-dark: #1e40af;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Enhanced Form Styles */
.form-input {
  transition: all 0.2s ease-in-out;
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

/* Ensure form elements have proper text color regardless of system color scheme */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
input[type="url"],
input[type="search"],
select,
textarea {
  color: #111827; /* text-gray-900 equivalent */
}

/* Ensure placeholder text is visible */
input::placeholder,
textarea::placeholder {
  color: #9ca3af; /* text-gray-400 equivalent */
}

/* Ensure select options have proper contrast */
select option {
  color: #111827;
  background-color: #ffffff;
}

.form-section {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85)
  );
  backdrop-filter: blur(10px);
}

.progress-bar {
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  box-shadow: 0 0 10px rgba(251, 191, 36, 0.4);
}

.file-drop-zone {
  background: linear-gradient(
    135deg,
    rgba(249, 250, 251, 0.8),
    rgba(243, 244, 246, 0.8)
  );
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
}

.file-drop-zone:hover {
  border-color: #fbbf24;
  background: linear-gradient(
    135deg,
    rgba(254, 243, 199, 0.8),
    rgba(253, 230, 138, 0.8)
  );
  transform: scale(1.01);
}

.file-drop-zone.active {
  border-color: #f59e0b;
  background: linear-gradient(
    135deg,
    rgba(254, 243, 199, 0.9),
    rgba(253, 230, 138, 0.9)
  );
  transform: scale(1.02);
}

.rtl-flip {
  transform: scaleX(-1);
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(-1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gold-dark);
}

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Gold gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--gold), var(--gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
