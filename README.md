# NewVision Business Website

A comprehensive, professional, multi-language website for NewVision - a company operating in four key business sectors: Commercial Brokerage, Fixed and Movable Assets Management, Investments & General Trading, and Advertising & Media Production.

## 🌟 Features

### Multi-Language Support

- **Arabic & English** with RTL (Right-to-Left) support
- Dynamic language switching
- Culturally appropriate content

### Business Sectors

1. **Commercial Brokerage** - Import/export facilitation, strategic deals
2. **Assets Management** - Real estate, vehicles, equipment management
3. **Investments & Trading** - Portfolio management, strategic partnerships
4. **Advertising & Media** - Video production, LED display advertising

### Technical Features

- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Hook Form** for form handling
- **i18next** for internationalization
- **Lucide React** for icons
- **Responsive Design** (Desktop, Tablet, Mobile)

## 🚀 Technologies Used

- **Frontend Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Forms**: React Hook Form with validation
- **Icons**: Lucide React
- **Internationalization**: react-i18next
- **Image Optimization**: Next.js Image component
- **SEO**: Built-in Next.js SEO features

## 📱 Pages & Sections

### 1. Home Page

- Hero section with compelling call-to-action
- Services overview with interactive cards
- Company statistics and achievements
- Features highlighting with animations
- Client testimonials

### 2. About Us

- Company story and background
- Mission, Vision, and Values
- Leadership team profiles
- Core values with detailed descriptions
- Achievement statistics

### 3. Services

- Interactive service selector
- Detailed service descriptions for all 4 sectors
- Process methodology (4-step approach)
- Benefits and features for each service
- Call-to-action sections

### 4. Portfolio

- Project showcase with filtering
- Case studies for each business sector
- Client testimonials
- Success metrics and achievements
- Interactive project filtering

### 5. Blog/News

- Article categories and filtering
- Featured articles
- Search functionality
- Newsletter subscription
- Author information and read time

### 6. Contact

- Multi-field contact form with validation
- Contact information cards
- Interactive map placeholder
- Social media links
- WhatsApp integration
- Working hours and location details

## 🎨 Design Features

### Color Scheme

- **Primary**: Gold (#d4af37) for trust and prestige
- **Secondary**: Navy Blue (#1e3a8a) for professionalism
- **Accent**: Various gradients for visual appeal

### Animations

- Smooth page transitions
- Scroll-triggered animations
- Hover effects and micro-interactions
- Loading states and form feedback

### Responsive Design

- Mobile-first approach
- Tablet and desktop optimizations
- Touch-friendly interactions
- Optimized images for all screen sizes

## 🌐 SEO Optimization

- Meta tags and Open Graph
- Structured data markup
- Sitemap generation
- Robots.txt configuration
- Image optimization
- Semantic HTML structure

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd newvision-website
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Run the development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
newvision-website/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── about/             # About page
│   │   ├── blog/              # Blog page
│   │   ├── contact/           # Contact page
│   │   ├── portfolio/         # Portfolio page
│   │   ├── services/          # Services page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Home page
│   │   └── sitemap.ts         # SEO sitemap
│   ├── components/            # Reusable components
│   │   ├── Header.tsx         # Navigation header
│   │   └── Footer.tsx         # Site footer
│   ├── contexts/              # React contexts
│   │   └── LanguageContext.tsx # Language management
│   └── lib/                   # Utilities and configurations
│       └── i18n.ts            # Internationalization setup
├── public/                    # Static assets
│   └── robots.txt            # SEO robots file
├── package.json              # Dependencies and scripts
├── tailwind.config.js        # Tailwind configuration
├── next.config.ts            # Next.js configuration
└── README.md                 # Project documentation
```

## 🔧 Configuration

### Language Configuration

Languages are configured in `src/lib/i18n.ts` with:

- English (en) - Default language
- Arabic (ar) - RTL support enabled

### Styling Configuration

- Tailwind CSS with custom color scheme
- CSS custom properties for brand colors
- Responsive breakpoints and utilities

### SEO Configuration

- Meta tags in layout files
- Sitemap generation
- Open Graph tags
- Structured data markup

## 📱 Mobile Responsiveness

The website is fully responsive with:

- Mobile-first design approach
- Touch-optimized navigation
- Responsive images and layouts
- Mobile-specific optimizations

## 🔒 Security Features

- Form validation and sanitization
- XSS protection
- CSRF protection (when forms are connected to backend)
- Secure headers configuration ready

## 🌍 Internationalization

### Supported Languages

- **English** (en) - Left-to-right
- **Arabic** (ar) - Right-to-left with full RTL support

### Translation Management

- Centralized translation files
- Context-aware translations
- RTL-specific styling adjustments

## 📊 Performance Optimization

- Next.js Image optimization
- Code splitting and lazy loading
- Optimized animations
- Compressed assets
- Efficient CSS delivery

## 🎯 Business Goals Alignment

The website is designed to:

- **Build Trust**: Professional design and comprehensive information
- **Generate Leads**: Strategic call-to-action placement
- **Showcase Expertise**: Detailed service descriptions and portfolio
- **Enable Communication**: Multiple contact methods
- **Support Growth**: Scalable architecture and SEO optimization

## 📞 Contact Information

For questions about this project:

- **Email**: <EMAIL>
- **Phone**: +20 1X XXXX XXXX
- **Location**: Mit Ghamr, Dakahlia, Egypt

## 📄 License

This project is proprietary to NewVision Business Solutions.

---

**Built with ❤️ for NewVision Business Solutions**
