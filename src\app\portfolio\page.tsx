"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion } from "framer-motion";
import {
  Building2,
  TrendingUp,
  Shield,
  Video,
  Eye,
  Calendar,
  MapPin,
  Award,
  Star,
} from "lucide-react";

const PortfolioPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeFilter, setActiveFilter] = useState("all");

  const filters = [
    { id: "all", label: isRTL ? "جميع المشاريع" : "All Projects" },
    {
      id: "brokerage",
      label: isRTL ? "الوساطة التجارية" : "Commercial Brokerage",
    },
    { id: "assets", label: isRTL ? "إدارة الأصول" : "Asset Management" },
    { id: "investments", label: isRTL ? "الاستثمارات" : "Investments" },
    { id: "media", label: isRTL ? "الإعلان والوسائط" : "Media & Advertising" },
  ];

  const projects = [
    {
      id: 1,
      category: "brokerage",
      title: isRTL
        ? "صفقة تجارية كبرى مع شركة عالمية"
        : "Major Commercial Deal with Global Corporation",
      description: isRTL
        ? "تسهيل صفقة استيراد بقيمة 50 مليون ريال"
        : "Facilitated import deal worth 50 million SAR",
      image:
        "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "2024",
      location: isRTL ? "ميت غمر" : "Mit Ghamr",
      value: isRTL ? "50 مليون ريال" : "50M SAR",
      result: isRTL ? "نجح المشروع بنسبة 100%" : "100% Success Rate",
    },
    {
      id: 2,
      category: "assets",
      title: isRTL
        ? "إدارة محفظة عقارية متنوعة"
        : "Diversified Real Estate Portfolio Management",
      description: isRTL
        ? "إدارة أصول عقارية بقيمة 200 مليون ريال"
        : "Managing real estate assets worth 200 million SAR",
      image:
        "https://images.unsplash.com/photo-1486406146494-da3fb1916ddf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "2023-2024",
      location: isRTL ? "ميت غمر، القاهرة" : "Mit Ghamr, Cairo",
      value: isRTL ? "200 مليون ريال" : "200M SAR",
      result: isRTL ? "زيادة العوائد 25%" : "25% Return Increase",
    },
    {
      id: 3,
      category: "investments",
      title: isRTL
        ? "استثمار في قطاع التكنولوجيا"
        : "Technology Sector Investment",
      description: isRTL
        ? "صندوق استثماري متخصص في الشركات الناشئة"
        : "Investment fund specializing in startups",
      image:
        "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "2023",
      location: isRTL ? "ميت غمر" : "Mit Ghamr",
      value: isRTL ? "75 مليون ريال" : "75M SAR",
      result: isRTL ? "عوائد 40%" : "40% Returns",
    },
    {
      id: 4,
      category: "media",
      title: isRTL ? "حملة إعلانية متكاملة" : "Integrated Advertising Campaign",
      description: isRTL
        ? "حملة إعلانية شاملة لعلامة تجارية كبرى"
        : "Comprehensive advertising campaign for major brand",
      image:
        "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "2024",
      location: isRTL ? "جمهورية مصر العربية" : "Arab Republic of Egypt",
      value: isRTL ? "15 مليون ريال" : "15M SAR",
      result: isRTL ? "زيادة المبيعات 60%" : "60% Sales Increase",
    },
    {
      id: 5,
      category: "brokerage",
      title: isRTL ? "توسط في صفقة تصدير كبرى" : "Major Export Deal Brokerage",
      description: isRTL
        ? "تسهيل صفقة تصدير للمنتجات الزراعية"
        : "Facilitated agricultural products export deal",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "2024",
      location: isRTL ? "الدمام" : "Dammam",
      value: isRTL ? "30 مليون ريال" : "30M SAR",
      result: isRTL ? "نجح في الوقت المحدد" : "On-time Success",
    },
    {
      id: 6,
      category: "assets",
      title: isRTL
        ? "إدارة أسطول المركبات التجارية"
        : "Commercial Fleet Management",
      description: isRTL
        ? "إدارة وتشغيل أسطول من 500 مركبة"
        : "Managing and operating fleet of 500 vehicles",
      image:
        "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "2023-2024",
      location: isRTL ? "ميت غمر" : "Mit Ghamr",
      value: isRTL ? "100 مليون ريال" : "100M SAR",
      result: isRTL ? "توفير 30% في التكاليف" : "30% Cost Reduction",
    },
  ];

  const filteredProjects =
    activeFilter === "all"
      ? projects
      : projects.filter((project) => project.category === activeFilter);

  const stats = [
    { number: "500+", label: isRTL ? "مشروع منجز" : "Completed Projects" },
    { number: "50+", label: isRTL ? "عميل راضٍ" : "Satisfied Clients" },
    {
      number: "2B+",
      label: isRTL ? "ريال قيمة المشاريع" : "SAR Project Value",
    },
    { number: "99%", label: isRTL ? "معدل النجاح" : "Success Rate" },
  ];

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80")',
          }}
        ></div>

        <div className="relative container mx-auto px-4 text-center text-white">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-6xl font-bold mb-6"
          >
            {isRTL ? "معرض أعمالنا" : "Our Portfolio"}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto"
          >
            {isRTL
              ? "اكتشف مشاريعنا الناجحة وقصص عملائنا الملهمة"
              : "Discover our successful projects and inspiring client stories"}
          </motion.p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-yellow-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setActiveFilter(filter.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all ${
                  activeFilter === filter.id
                    ? "bg-yellow-500 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group"
              >
                <div className="aspect-video overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>

                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full">
                      {filters.find((f) => f.id === project.category)?.label}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Calendar className="w-4 h-4 mr-1" />
                      {project.date}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-yellow-600 transition-colors">
                    {project.title}
                  </h3>

                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center text-gray-500">
                      <MapPin className="w-4 h-4 mr-2" />
                      {project.location}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-green-600">
                        {project.value}
                      </span>
                      <span className="text-yellow-600 font-medium">
                        {project.result}
                      </span>
                    </div>
                  </div>

                  <button className="w-full mt-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-black font-semibold py-2 px-4 rounded-lg hover:from-yellow-500 hover:to-yellow-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2">
                    <Eye className="w-4 h-4" />
                    <span>{isRTL ? "عرض التفاصيل" : "View Details"}</span>
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Client Testimonials */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {isRTL ? "آراء عملائنا" : "Client Testimonials"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {isRTL
                ? "ما يقوله عملاؤنا عن خدماتنا وجودة أعمالنا"
                : "What our clients say about our services and work quality"}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((testimonial, index) => (
              <motion.div
                key={testimonial}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-8 shadow-lg"
              >
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-5 h-5 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic">
                  {isRTL
                    ? `"خدمة ممتازة ونتائج فاقت توقعاتنا. فريق نيو فيجن محترف ومتفهم لاحتياجات العمل."`
                    : `"Excellent service and results exceeded our expectations. NewVision team is professional and understanding of business needs."`}
                </p>
                <div className="flex items-center">
                  <img
                    src={`https://images.unsplash.com/photo-${
                      index === 0
                        ? "1472099645785-5658abf4ff4e"
                        : index === 1
                        ? "1494790108755-2616b612b96b"
                        : "1507003211169-0a1dd7228f2d"
                    }?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80`}
                    alt="Client"
                    className="w-12 h-12 rounded-full mr-4"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900">
                      {isRTL
                        ? index === 0
                          ? "أحمد الشمري"
                          : index === 1
                          ? "فاطمة النور"
                          : "خالد العتيبي"
                        : index === 0
                        ? "Ahmed Al-Shamri"
                        : index === 1
                        ? "Fatima Al-Noor"
                        : "Khalid Al-Otaibi"}
                    </h4>
                    <p className="text-gray-500 text-sm">
                      {isRTL ? "مدير تنفيذي" : "Executive Manager"}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default PortfolioPage;
