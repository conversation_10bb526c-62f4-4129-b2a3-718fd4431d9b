"use client";

import { useLanguage } from "@/contexts/LanguageContext";
import { useLayoutEffect } from "react";

export default function AppWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const { language, isRTL } = useLanguage();

  useLayoutEffect(() => {
    // Set initial attributes to prevent hydration mismatch
    document.documentElement.lang = language;
    document.documentElement.dir = isRTL ? "rtl" : "ltr";
  }, [language, isRTL]);

  return <>{children}</>;
}
