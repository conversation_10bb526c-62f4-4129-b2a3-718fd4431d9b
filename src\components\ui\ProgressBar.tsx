"use client";

import React from "react";
import { motion } from "framer-motion";

interface ProgressBarProps {
  progress: number;
  label?: string;
  showPercentage?: boolean;
  variant?: "default" | "gradient" | "glow";
  size?: "sm" | "md" | "lg";
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label,
  showPercentage = true,
  variant = "default",
  size = "md",
  className = "",
}) => {
  const variants = {
    default: "bg-primary-600",
    gradient: "bg-gradient-to-r from-primary-400 to-primary-600",
    glow: "bg-gradient-to-r from-primary-400 to-primary-600 shadow-glow",
  };

  const sizes = {
    sm: "h-2",
    md: "h-3",
    lg: "h-4",
  };

  return (
    <div className={`w-full ${className}`}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && <span className="text-sm font-medium text-gray-700">{label}</span>}
          {showPercentage && (
            <span className="text-sm font-medium text-gray-600">{Math.round(progress)}%</span>
          )}
        </div>
      )}
      
      <div className={`w-full bg-gray-200 rounded-full overflow-hidden ${sizes[size]}`}>
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${Math.min(Math.max(progress, 0), 100)}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
          className={`${sizes[size]} ${variants[variant]} rounded-full transition-all duration-300`}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
