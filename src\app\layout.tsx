import type { Metadata } from "next";
import "./globals.css";
import { LanguageProvider } from "@/contexts/LanguageContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import AppWrapper from "@/components/AppWrapper";
import FloatingActionButton from "@/components/ui/FloatingActionButton";
import { GeistSans, GeistMono } from "geist/font";

export const metadata: Metadata = {
  title:
    "NewVision - Business Solutions | Commercial Brokerage & Asset Management",
  description:
    "Leading provider of commercial brokerage, asset management, investments, and media production services. Excellence in business solutions across four key sectors.",
  keywords:
    "commercial brokerage, asset management, investments, trading, media production, business solutions",
  authors: [{ name: "NewVision" }],
  openGraph: {
    title: "NewVision - Business Solutions",
    description:
      "Excellence in commercial brokerage, asset management, investments, and media production.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html className={`${GeistSans.variable} ${GeistMono.variable}`}>
      <body className="antialiased">
        <LanguageProvider>
          <AppWrapper>
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-grow">{children}</main>
              <Footer />
              <FloatingActionButton
                onChatClick={() => console.log("Chat clicked")}
                onCallClick={() => window.open("tel:+201XXXXXXXX")}
              />
            </div>
          </AppWrapper>
        </LanguageProvider>
      </body>
    </html>
  );
}
