"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Building2,
  TrendingUp,
  Shield,
  Video,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Award,
  Target,
} from "lucide-react";

const HomePage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const services = [
    {
      icon: Building2,
      title: t("services.brokerage.title"),
      description: t("services.brokerage.description"),
      color: "from-blue-500 to-blue-600",
      link: "/services#brokerage",
    },
    {
      icon: Shield,
      title: t("services.assets.title"),
      description: t("services.assets.description"),
      color: "from-green-500 to-green-600",
      link: "/services#assets",
    },
    {
      icon: TrendingUp,
      title: t("services.investments.title"),
      description: t("services.investments.description"),
      color: "from-purple-500 to-purple-600",
      link: "/services#investments",
    },
    {
      icon: Video,
      title: t("services.media.title"),
      description: t("services.media.description"),
      color: "from-red-500 to-red-600",
      link: "/services#media",
    },
  ];

  const stats = [
    { number: "500+", label: isRTL ? "عميل راضٍ" : "Happy Clients" },
    { number: "1000+", label: isRTL ? "مشروع مكتمل" : "Projects Completed" },
    { number: "15+", label: isRTL ? "سنة خبرة" : "Years Experience" },
    { number: "50+", label: isRTL ? "خبير متخصص" : "Expert Team" },
  ];

  const features = [
    isRTL ? "خبرة موثوقة في السوق" : "Trusted Market Expertise",
    isRTL ? "حلول مبتكرة ومخصصة" : "Innovative Custom Solutions",
    isRTL ? "دعم متواصل على مدار الساعة" : "24/7 Professional Support",
    isRTL ? "نتائج مضمونة وموثوقة" : "Guaranteed Results",
  ];

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80")',
          }}
        ></div>

        <div className="relative container mx-auto px-4 text-white">
          <div className="max-w-4xl">
            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-5xl md:text-7xl font-bold mb-6 leading-tight"
            >
              <span className="block">{t("hero.title")}</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed"
            >
              {t("hero.subtitle")}
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 rtl:sm:space-x-reverse"
            >
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black px-8 py-4 rounded-lg font-semibold hover:from-yellow-500 hover:to-yellow-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <span>{t("hero.cta")}</span>
                <ArrowRight className={`w-5 h-5 ${isRTL ? "rtl-flip" : ""}`} />
              </Link>

              <Link
                href="/about"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-gray-900 transition-all flex items-center justify-center"
              >
                {t("common.learnMore")}
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
          </div>
        </motion.div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-yellow-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t("services.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t("services.subtitle")}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Link href={service.link}>
                  <div className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 p-8 h-full border hover:border-yellow-200 group-hover:scale-105">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-lg flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}
                    >
                      <service.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-yellow-600 transition-colors">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                {isRTL ? "لماذا نحن الخيار الأفضل؟" : "Why Choose NewVision?"}
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                {isRTL
                  ? "نقدم حلولاً تجارية متكاملة بأعلى معايير الجودة والاحترافية"
                  : "We deliver comprehensive business solutions with the highest standards of quality and professionalism"}
              </p>

              <div className="space-y-4">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-3 rtl:space-x-reverse"
                  >
                    <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Business team"
                className="rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-yellow-400 text-black p-6 rounded-lg shadow-xl">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Star className="w-6 h-6" />
                  <span className="font-bold text-lg">
                    {isRTL ? "تقييم ممتاز" : "Excellent Rating"}
                  </span>
                </div>
                <div className="text-sm mt-1">
                  {isRTL ? "4.9/5 من عملائنا" : "4.9/5 from our clients"}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-blue-900">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {isRTL
                ? "هل أنت مستعد لبدء رحلة النجاح؟"
                : "Ready to Start Your Success Journey?"}
            </h2>
            <p className="text-xl text-gray-200 mb-8">
              {isRTL
                ? "تواصل معنا اليوم واحصل على استشارة مجانية لتحقيق أهدافك التجارية"
                : "Contact us today for a free consultation to achieve your business goals"}
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:sm:space-x-reverse">
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black px-8 py-4 rounded-lg font-semibold hover:from-yellow-500 hover:to-yellow-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <span>{t("hero.cta")}</span>
                <ArrowRight className={`w-5 h-5 ${isRTL ? "rtl-flip" : ""}`} />
              </Link>
              <Link
                href="/services"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-gray-900 transition-all"
              >
                {t("services.title")}
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
