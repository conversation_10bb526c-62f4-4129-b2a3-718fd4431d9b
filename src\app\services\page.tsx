"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion, AnimatePresence } from "framer-motion";
import {
  Building2,
  TrendingUp,
  Shield,
  Video,
  CheckCircle,
  ArrowRight,
  Globe,
  BarChart,
  Camera,
  Home,
  Car,
  Monitor,
  Briefcase,
  PieChart,
  Users,
  Target,
} from "lucide-react";

const ServicesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeTab, setActiveTab] = useState(0);

  const services = [
    {
      id: "brokerage",
      icon: Building2,
      title: t("services.brokerage.title"),
      description: t("services.brokerage.description"),
      color: "from-blue-500 to-blue-600",
      features: isRTL
        ? [
            "تسهيل الصفقات التجارية الكبرى",
            "خدمات الاستيراد والتصدير",
            "تجارة السلع الاستراتيجية",
            "الوساطة في العقود الحكومية",
            "استشارات السوق المتخصصة",
            "إدارة سلاسل التوريد",
          ]
        : [
            "Major Commercial Deal Facilitation",
            "Import & Export Services",
            "Strategic Commodity Trading",
            "Government Contract Brokerage",
            "Specialized Market Consulting",
            "Supply Chain Management",
          ],
      benefits: isRTL
        ? [
            "خبرة 15+ سنة في السوق السعودي",
            "شبكة واسعة من الشركاء الدوليين",
            "معرفة عميقة بالقوانين التجارية",
            "خدمة عملاء متخصصة 24/7",
          ]
        : [
            "15+ Years Saudi Market Experience",
            "Extensive International Partner Network",
            "Deep Knowledge of Trade Regulations",
            "Specialized 24/7 Customer Service",
          ],
    },
    {
      id: "assets",
      icon: Shield,
      title: t("services.assets.title"),
      description: t("services.assets.description"),
      color: "from-green-500 to-green-600",
      features: isRTL
        ? [
            "إدارة الأصول العقارية",
            "إدارة أسطول المركبات",
            "إدارة المعدات والآلات",
            "تقييم الأصول المعتمد",
            "نظام مراقبة متقدم",
            "التأمين وإدارة المخاطر",
          ]
        : [
            "Real Estate Asset Management",
            "Fleet Management Services",
            "Equipment & Machinery Management",
            "Certified Asset Valuation",
            "Advanced Monitoring Systems",
            "Insurance & Risk Management",
          ],
      benefits: isRTL
        ? [
            "تحسين عوائد الاستثمار",
            "تقليل تكاليف التشغيل",
            "زيادة القيمة السوقية للأصول",
            "تقارير دورية مفصلة",
          ]
        : [
            "Improved Investment Returns",
            "Reduced Operating Costs",
            "Increased Market Value of Assets",
            "Detailed Periodic Reports",
          ],
    },
    {
      id: "investments",
      icon: TrendingUp,
      title: t("services.investments.title"),
      description: t("services.investments.description"),
      color: "from-purple-500 to-purple-600",
      features: isRTL
        ? [
            "إدارة المحافظ الاستثمارية",
            "الشراكات الاستراتيجية",
            "دراسات الجدوى الاقتصادية",
            "التخطيط المالي طويل المدى",
            "استشارات الاستثمار",
            "تحليل الأسواق المالية",
          ]
        : [
            "Investment Portfolio Management",
            "Strategic Partnerships",
            "Economic Feasibility Studies",
            "Long-term Financial Planning",
            "Investment Consulting",
            "Financial Market Analysis",
          ],
      benefits: isRTL
        ? [
            "عوائد استثمارية متنوعة",
            "إدارة مخاطر محترفة",
            "استراتيجيات مخصصة",
            "مراقبة مستمرة للأداء",
          ]
        : [
            "Diversified Investment Returns",
            "Professional Risk Management",
            "Customized Strategies",
            "Continuous Performance Monitoring",
          ],
    },
    {
      id: "media",
      icon: Video,
      title: t("services.media.title"),
      description: t("services.media.description"),
      color: "from-red-500 to-red-600",
      features: isRTL
        ? [
            "إنتاج الفيديو المهني",
            "إعلانات الشاشات الخارجية",
            "التصوير الفوتوغرافي",
            "التسويق الرقمي",
            "تصميم الهوية البصرية",
            "إدارة حملات الإعلان",
          ]
        : [
            "Professional Video Production",
            "Outdoor LED Display Advertising",
            "Professional Photography",
            "Digital Marketing",
            "Visual Identity Design",
            "Advertising Campaign Management",
          ],
      benefits: isRTL
        ? [
            "إنتاج بجودة عالمية",
            "فريق إبداعي متخصص",
            "تقنيات حديثة ومتطورة",
            "حلول إعلانية متكاملة",
          ]
        : [
            "World-class Production Quality",
            "Specialized Creative Team",
            "Modern Advanced Technologies",
            "Comprehensive Advertising Solutions",
          ],
    },
  ];

  const processSteps = isRTL
    ? [
        {
          step: "01",
          title: "التقييم والتحليل",
          description: "نحلل احتياجاتكم ونقيم الوضع الحالي لتحديد أفضل الحلول",
        },
        {
          step: "02",
          title: "التخطيط الاستراتيجي",
          description: "نضع خطة استراتيجية مفصلة تتماشى مع أهدافكم التجارية",
        },
        {
          step: "03",
          title: "التنفيذ والمتابعة",
          description:
            "ننفذ الخطة بدقة ونتابع التقدم لضمان تحقيق النتائج المطلوبة",
        },
        {
          step: "04",
          title: "التقييم والتحسين",
          description:
            "نقيم النتائج ونطور الحلول باستمرار لضمان التحسن المستمر",
        },
      ]
    : [
        {
          step: "01",
          title: "Assessment & Analysis",
          description:
            "We analyze your needs and evaluate the current situation to determine the best solutions",
        },
        {
          step: "02",
          title: "Strategic Planning",
          description:
            "We develop a detailed strategic plan that aligns with your business objectives",
        },
        {
          step: "03",
          title: "Implementation & Monitoring",
          description:
            "We execute the plan precisely and monitor progress to ensure desired results",
        },
        {
          step: "04",
          title: "Evaluation & Improvement",
          description:
            "We evaluate results and continuously develop solutions to ensure ongoing improvement",
        },
      ];

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80")',
          }}
        ></div>

        <div className="relative container mx-auto px-4 text-center text-white">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-6xl font-bold mb-6"
          >
            {t("services.title")}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto"
          >
            {t("services.subtitle")}
          </motion.p>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {services.map((service, index) => {
              const Icon = service.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center group cursor-pointer"
                  onClick={() => setActiveTab(index)}
                >
                  <div
                    className={`w-20 h-20 bg-gradient-to-r ${
                      service.color
                    } rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform ${
                      activeTab === index ? "ring-4 ring-yellow-400" : ""
                    }`}
                  >
                    <Icon className="w-10 h-10 text-white" />
                  </div>
                  <h3
                    className={`text-xl font-bold mb-4 transition-colors ${
                      activeTab === index ? "text-yellow-600" : "text-gray-900"
                    }`}
                  >
                    {service.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {service.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Detailed Service Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${services[activeTab].color} rounded-lg flex items-center justify-center`}
                    >
                      {(() => {
                        const Icon = services[activeTab].icon;
                        return <Icon className="w-8 h-8 text-white" />;
                      })()}
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900">
                      {services[activeTab].title}
                    </h2>
                  </div>

                  <p className="text-lg text-gray-600 leading-relaxed">
                    {services[activeTab].description}
                  </p>

                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-gray-900">
                      {isRTL ? "خدماتنا تشمل:" : "Our Services Include:"}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {services[activeTab].features.map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-3 rtl:space-x-reverse"
                        >
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-gray-900">
                      {isRTL ? "المزايا الرئيسية:" : "Key Benefits:"}
                    </h3>
                    <div className="space-y-3">
                      {services[activeTab].benefits.map((benefit, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-3 rtl:space-x-reverse"
                        >
                          <ArrowRight
                            className={`w-5 h-5 text-yellow-600 flex-shrink-0 ${
                              isRTL ? "rtl-flip" : ""
                            }`}
                          />
                          <span className="text-gray-700 font-medium">
                            {benefit}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="relative">
                  <img
                    src={`https://images.unsplash.com/photo-${
                      activeTab === 0
                        ? "1560472354-b33ff0c44a43"
                        : activeTab === 1
                        ? "1486406146494-da3fb1916ddf"
                        : activeTab === 2
                        ? "1611974789855-9c2a0a7236a3"
                        : "1598300042247-d088f8ab3a91"
                    }?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                    alt={services[activeTab].title}
                    className="rounded-2xl shadow-2xl"
                  />
                  <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-xl border">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div
                        className={`w-3 h-3 bg-gradient-to-r ${services[activeTab].color} rounded-full`}
                      ></div>
                      <span className="font-bold text-gray-900">
                        {isRTL ? "خدمة متميزة" : "Premium Service"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {isRTL ? "منهجية العمل" : "Our Process"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {isRTL
                ? "نتبع منهجية علمية مدروسة لضمان تحقيق أفضل النتائج لعملائنا"
                : "We follow a scientific and studied methodology to ensure the best results for our clients"}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center relative"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6 text-black font-bold text-xl">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>

                {index < processSteps.length - 1 && (
                  <div
                    className={`hidden lg:block absolute top-8 ${
                      isRTL ? "right-0" : "left-full"
                    } w-full h-0.5 bg-gray-300`}
                  >
                    <div className="w-4 h-4 bg-yellow-400 rounded-full absolute top-1/2 right-0 transform -translate-y-1/2"></div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-blue-900">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {isRTL
                ? "هل تحتاج لخدماتنا المتخصصة؟"
                : "Need Our Specialized Services?"}
            </h2>
            <p className="text-xl text-gray-200 mb-8">
              {isRTL
                ? "تواصل معنا اليوم للحصول على استشارة مجانية ومخصصة لاحتياجاتك"
                : "Contact us today for a free consultation tailored to your needs"}
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:sm:space-x-reverse">
              <a
                href="/contact"
                className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black px-8 py-4 rounded-lg font-semibold hover:from-yellow-500 hover:to-yellow-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <span>{isRTL ? "احصل على استشارة" : "Get Consultation"}</span>
                <ArrowRight className={`w-5 h-5 ${isRTL ? "rtl-flip" : ""}`} />
              </a>
              <a
                href="tel:+966XXXXXXXX"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-gray-900 transition-all"
              >
                {isRTL ? "اتصل بنا" : "Call Us"}
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;
